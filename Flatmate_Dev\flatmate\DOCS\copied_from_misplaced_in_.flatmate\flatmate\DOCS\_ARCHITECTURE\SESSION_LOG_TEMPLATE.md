# Session Log Template

Copy this template for every work session to maintain real-time documentation.

---

## SESSION_LOG.md Template

```markdown
# Session Log: <Session Name>

**Date**: YYYY-MM-DD  
**Type**: [FEATURE|REFACTOR|TROUBLESHOOT|MAINTENANCE]  
**Status**: IN_PROGRESS  
**Start Time**: HH:MM  
**AI Assistant**: [Claude/GPT/etc.]  
**Session ID**: [For reference across multiple AI chats]

---

## Objective
[What are we trying to accomplish this session?]

## Context
[Why is this work needed? What prompted it? Link to previous sessions if applicable]

## Success Criteria
- [ ] [Specific outcome 1]
- [ ] [Specific outcome 2]
- [ ] [Specific outcome 3]

---

## Real-Time Log

### [HH:MM] Session Start
- **Action**: Initialized session documentation
- **Context**: [Brief description of starting point]
- **Plan**: [High-level approach for this session]

### [HH:MM] [Phase/Step Name]
- **Action**: [What was done]
- **Discovery**: [What was learned/found]
- **Decision**: [Any choices made]
- **Next**: [What to do next]

### [HH:MM] Issue Encountered
- **Problem**: [Clear description of the issue]
- **Symptoms**: [What we observed]
- **Attempted Solution**: [What was tried]
- **Result**: [What happened]
- **Resolution**: [How it was ultimately solved]
- **Lesson**: [What we learned for next time]

### [HH:MM] Code Changes
- **File**: `path/to/file.py`
- **Change**: [Description of what was modified]
- **Reason**: [Why this change was needed]
- **Testing**: [How it was verified]

### [HH:MM] Testing Phase
- **Test Type**: [Unit/Integration/Manual/etc.]
- **Test Target**: [What was being tested]
- **Result**: [Pass/Fail/Partial]
- **Issues Found**: [Any problems discovered]
- **Actions Taken**: [How issues were addressed]

### [HH:MM] Architecture Decision
- **Decision**: [What was decided]
- **Alternatives**: [Other options considered]
- **Rationale**: [Why this choice was made]
- **Impact**: [What this affects]
- **Future Considerations**: [Things to watch out for]

### [HH:MM] Break/Interruption
- **Reason**: [Why session was paused]
- **Duration**: [How long]
- **Context on Resume**: [State when returning]

### [HH:MM] Session End
- **Status**: [COMPLETE|ON_HOLD|NEEDS_FOLLOWUP]
- **End Time**: HH:MM
- **Duration**: [Total session time]
- **Accomplishments**: [What was achieved]
- **Next Steps**: [What needs to happen next]
- **Blockers**: [Any impediments identified]

---

## Key Decisions Made
1. **[Decision Topic]**: [What was decided and why]
2. **[Decision Topic]**: [What was decided and why]
3. **[Decision Topic]**: [What was decided and why]

## Files Modified
- `path/to/file1.py` - [Description of changes made]
- `path/to/file2.py` - [Description of changes made]
- `path/to/config.yaml` - [Description of changes made]

## Evidence Collected
- `EVIDENCE/error_logs/session_errors.log` - [Description]
- `EVIDENCE/screenshots/before_state.png` - [Description]
- `EVIDENCE/screenshots/after_state.png` - [Description]
- `EVIDENCE/code_samples/original_implementation.py` - [Description]

## Technical Debt Identified
1. **[Issue Description]**: [Problem and recommended solution]
2. **[Issue Description]**: [Problem and recommended solution]
3. **[Issue Description]**: [Problem and recommended solution]

## Performance Notes
- **Memory Usage**: [Any observations]
- **Speed**: [Any performance impacts noted]
- **Resource Usage**: [CPU, disk, network observations]

## User Feedback/Requirements
- **Feedback Received**: [Any user input during session]
- **Requirements Clarified**: [New understanding gained]
- **Scope Changes**: [Any adjustments to objectives]

## Integration Points
- **Components Affected**: [What parts of the system were touched]
- **Dependencies**: [What this work depends on or affects]
- **Breaking Changes**: [Any compatibility issues introduced]

## Testing Summary
- **Tests Written**: [New tests created]
- **Tests Modified**: [Existing tests updated]
- **Test Results**: [Overall testing outcome]
- **Coverage**: [Test coverage notes]

## Session Outcome
**Overall Result**: [SUCCESS|PARTIAL_SUCCESS|BLOCKED|FAILED]

**Summary**: [2-3 sentence summary of what was accomplished]

**Value Delivered**: [What benefit this provides]

**Confidence Level**: [How confident are we in the solution - High/Medium/Low]

## Lessons Learned (MANDATORY - Self-Improvement System)

### What Worked Well
- **Process**: [Workflow/approach that was particularly effective]
- **Technical**: [Code pattern, tool, or technique that worked great]
- **AI Collaboration**: [Prompting or interaction pattern that was successful]
- **Time Management**: [Scheduling or productivity insight]

### What Didn't Work
- **Process**: [Workflow issue that caused friction or wasted time]
- **Technical**: [Approach that failed or was inefficient]
- **AI Collaboration**: [Interaction pattern that was confusing or ineffective]
- **Time Management**: [Estimation error or productivity blocker]

### Key Insights Gained
- **Architecture**: [New understanding about system design or structure]
- **Codebase**: [Pattern recognition or deeper understanding of existing code]
- **Tools**: [New capability discovered or better way to use existing tools]
- **Problem-Solving**: [Debugging technique or analytical approach that proved valuable]

### Process Improvements Identified
- **Immediate**: [Changes that can be applied right now]
- **Template Updates**: [Documentation or templates that need improvement]
- **Workflow Enhancements**: [Process changes that would improve future sessions]
- **Tool/Automation Opportunities**: [Repetitive tasks that could be automated]

### For Next Session
- **Apply Immediately**: [Lessons that should be used in the very next work session]
- **Remember**: [Important context or insights to keep in mind]
- **Avoid**: [Mistakes or approaches to not repeat]
- **Try**: [New approaches or techniques to experiment with]

---

## Handoff Notes (If Session Incomplete)
**For Next Session**:
- **Current State**: [Exactly where we left off]
- **Immediate Next Steps**: [What to do first]
- **Context to Remember**: [Important details for continuation]
- **Potential Issues**: [Things to watch out for]

**For Other Developers**:
- **Background**: [Context they need to understand]
- **Current Implementation**: [What exists now]
- **Intended Direction**: [Where this is heading]
- **Key Decisions**: [Important choices made]

---

**Session Completed**: YYYY-MM-DD HH:MM  
**Next Session Planned**: [Date/time if known]  
**Follow-up Required**: [Yes/No - what kind]
```

---

## Usage Instructions

### Starting a Session:
1. **Copy this template** to `flatmate/DOCS/_FEATURES/<session_name>/SESSION_LOG.md`
2. **Fill in header information** (date, type, objective)
3. **Set success criteria** - what defines success for this session
4. **Begin real-time logging** - update as you work

### During the Session:
1. **Log every significant action** - don't wait until the end
2. **Record decisions and rationale** - why choices were made
3. **Document issues immediately** - while context is fresh
4. **Note all file changes** - what was modified and why
5. **Collect evidence** - save logs, screenshots, code samples

### Ending the Session:
1. **Update session status** and end time
2. **Summarize accomplishments** and next steps
3. **Complete all sections** - don't leave blanks
4. **Create CHANGELOG.md** following update-docs protocol
5. **Archive evidence** to EVIDENCE/ folder

### Quality Checks:
- [ ] Can another developer understand what happened?
- [ ] Are all decisions explained with rationale?
- [ ] Is the current state clearly documented?
- [ ] Are next steps actionable and specific?
- [ ] Is all evidence properly archived?

---

**This template ensures comprehensive documentation of every work session, preserving context and decisions for future reference.**

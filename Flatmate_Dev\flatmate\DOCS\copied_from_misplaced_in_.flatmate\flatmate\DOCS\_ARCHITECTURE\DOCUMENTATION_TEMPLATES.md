# Documentation Templates

Standard templates for consistent documentation across the project.

---

## CHANGELOG.md Template

Use this template for feature changelogs in `_FEATURES/<feature_name>/CHANGELOG.md`:

```markdown
# <Feature Name> - Changelog

**Date**: YYYY-MM-DD  
**Status**: [IN_PROGRESS|COMPLETED|ON_HOLD|CANCELLED]  
**Session**: [Brief session description]

---

## Summary

[What was accomplished this session - 2-3 sentences]

---

## Changes Made

### 1. **[Category of Change]**

**Description**: [What was changed and why]

**Files Modified:**
- `path/to/file.py` - [What was changed]
- `path/to/other.py` - [What was changed]

**Key Changes:**
- [Specific change 1]
- [Specific change 2]
- [Specific change 3]

### 2. **[Another Category]**

[Repeat structure as needed]

---

## Files Modified

### Core Files:
1. **`path/to/main/file.py`**
   - [Description of changes]
   - [Methods added/modified]
   - [Why changes were needed]

2. **`path/to/other/file.py`**
   - [Description of changes]

### Supporting Files:
3. **`path/to/config.py`**
   - [Description of changes]

---

## Testing Results

✅ **[Test Category 1]**: [What was verified]  
✅ **[Test Category 2]**: [What was verified]  
❌ **[Known Issue]**: [What still needs work]  

---

## Architecture Benefits

### 1. **[Benefit Category]**
- [Specific improvement]
- [Why this matters]

### 2. **[Another Benefit]**
- [Specific improvement]
- [Impact on maintainability/performance/etc.]

---

## Known Issues Resolved

1. ✅ **[Issue Description]** - [How it was fixed]
2. ✅ **[Another Issue]** - [Solution implemented]

---

## Technical Debt Identified

1. **[Debt Item]**: [Description and recommended solution]
2. **[Another Item]**: [Future refactoring opportunity]

---

## Future Enhancements

1. **[Enhancement Opportunity]**: [Description and potential value]
2. **[Another Opportunity]**: [What could be improved]

---

## Cost Analysis (If Applicable)

**Development Time**: [Estimate]  
**Value Delivered**: [Key benefits]  
**ROI**: [High/Medium/Low] - [Justification]

---

**Session Completed**: YYYY-MM-DD  
**Next Steps**: [What should happen next]
```

---

## Technical Debt Template

Use this for `_ARCHITECTURE/TECHNICAL_DEBT.md`:

```markdown
# Technical Debt Registry

## Active Debt Items

### [PRIORITY] - [Debt Item Name]
**Identified**: YYYY-MM-DD  
**Impact**: [High/Medium/Low]  
**Effort**: [High/Medium/Low]  

**Description**: [What is the issue]

**Current Workaround**: [How we're handling it now]

**Proper Solution**: [What should be done]

**Risks**: [What happens if we don't fix it]

**Dependencies**: [What needs to happen first]

---

## Resolved Debt Items

### [RESOLVED] - [Item Name]
**Resolved**: YYYY-MM-DD  
**Solution**: [How it was fixed]  
**Files Changed**: [List of modified files]

---
```

---

## Architecture Decision Record (ADR) Template

Use this for significant architectural decisions:

```markdown
# ADR-XXXX: [Decision Title]

**Date**: YYYY-MM-DD  
**Status**: [Proposed|Accepted|Deprecated|Superseded]  
**Context**: [Feature/Refactoring that prompted this decision]

---

## Context

[What is the issue that we're seeing that is motivating this decision or change?]

---

## Decision

[What is the change that we're proposing or have agreed to implement?]

---

## Rationale

[Why did we choose this option over alternatives?]

### Alternatives Considered

1. **[Alternative 1]**
   - Pros: [Benefits]
   - Cons: [Drawbacks]
   - Why rejected: [Reason]

2. **[Alternative 2]**
   - Pros: [Benefits]
   - Cons: [Drawbacks]
   - Why rejected: [Reason]

---

## Consequences

### Positive
- [Benefit 1]
- [Benefit 2]

### Negative
- [Trade-off 1]
- [Trade-off 2]

### Neutral
- [Impact 1]
- [Impact 2]

---

## Implementation Notes

- [Key implementation detail 1]
- [Key implementation detail 2]
- [Migration steps if needed]

---

## Success Metrics

- [How we'll know this decision was successful]
- [Measurable outcomes]

---

**Decision Made By**: [Who decided]  
**Stakeholders**: [Who was consulted]  
**Review Date**: [When to revisit this decision]
```

---

## Usage Guidelines

### When to Use Each Template

1. **CHANGELOG.md**: After every refactoring session (MANDATORY)
2. **Technical Debt**: When architectural issues are identified
3. **ADR**: For significant architectural decisions that affect multiple components

### Template Customization

- **Add sections** as needed for specific features
- **Remove sections** that don't apply
- **Maintain consistency** in structure and formatting
- **Update templates** based on lessons learned

### Quality Standards

- **Be Specific**: Include file paths, method names, line numbers
- **Be Complete**: Don't leave sections empty - write "N/A" if not applicable
- **Be Honest**: Document both successes and failures
- **Be Forward-Looking**: Always include future considerations

---

**These templates ensure consistent, comprehensive documentation across all features and architectural decisions.**

---
description: "Enhanced troubleshooting workflow with systematic problem-solving and documentation"
type: "specialized_workflow"
version: "2.0"
agents: ["windsurf", "augment", "kilo", "claude", "any"]
integration: "Uses unified-work-session.md for session management"
---

# Enhanced Troubleshooting Workflow

**Purpose**: Systematic problem-solving with comprehensive documentation  
**Integration**: Use with unified work session protocol  
**Enhanced**: Includes evidence collection and lesson capture  

---

## Pre-Troubleshooting Setup

### **Session Initialization**
1. **Follow**: `unified-work-session.md` for session setup
2. **Session Type**: TROUBLESHOOT
3. **Create**: `TROUBLESHOOT_LOG.md` in session folder
4. **Prepare**: Evidence collection folders

### **Problem Context**
```markdown
# Troubleshoot Log: [Issue Name]

## Problem Statement
**What is broken?**: [Clear description]
**Expected behavior**: [What should happen]
**Actual behavior**: [What is happening]
**Impact**: [Who/what is affected]
**Urgency**: [High/Medium/Low]

## Environment
**System**: [OS, versions, environment details]
**Last working**: [When did it last work correctly]
**Recent changes**: [What changed recently]
```

---

## Systematic Troubleshooting Process

### **Step 1: Clarify the Problem** (10-15 minutes)
**Objective**: Get crystal clear on what's actually wrong

#### Actions:
- [ ] **Document exact symptoms** - What you observe vs. what you expect
- [ ] **Identify minimal reproducible scenario** - Simplest way to trigger the issue
- [ ] **Determine scope** - What works, what doesn't, what's affected
- [ ] **Gather initial context** - When started, what changed, who reported

#### Documentation:
```markdown
### Problem Clarification
**Symptoms**: [Exact observations]
**Reproduction Steps**: 
1. [Step 1]
2. [Step 2]
3. [Result]

**Scope**: 
- **Affected**: [What's broken]
- **Working**: [What still works]
- **Environment**: [Where it happens]
```

### **Step 2: Gather Evidence** (15-20 minutes)
**Objective**: Collect all relevant information before making changes

#### Actions:
- [ ] **Check logs** - Application logs, system logs, error logs
- [ ] **Examine error messages** - Full stack traces, error codes
- [ ] **Review recent changes** - Git history, deployments, config changes
- [ ] **Test related functionality** - What else might be affected
- [ ] **Document system state** - Current configuration, environment

#### Evidence Collection:
```bash
# Save logs
cp /path/to/error.log EVIDENCE/error_logs/$(date +%Y%m%d_%H%M%S)_error.log

# Screenshot error states
# Save to EVIDENCE/screenshots/

# Export relevant code
git show HEAD~5..HEAD > EVIDENCE/code_samples/recent_changes.diff
```

#### Documentation:
```markdown
### Evidence Collected
**Error Logs**: [Location and key findings]
**Stack Traces**: [Relevant traces with line numbers]
**Recent Changes**: [Git commits, config changes]
**System State**: [Current configuration]
**Related Issues**: [Similar problems found]
```

### **Step 3: Isolate the Issue** (10-15 minutes)
**Objective**: Narrow down to the specific cause

#### Actions:
- [ ] **Reproduce in isolation** - Minimal test case
- [ ] **Eliminate variables** - Test with different inputs/conditions
- [ ] **Check dependencies** - External services, libraries, databases
- [ ] **Add targeted logging** - Strategic debug output (don't obscure errors)
- [ ] **Test boundaries** - Where does it work vs. not work

#### Documentation:
```markdown
### Issue Isolation
**Minimal Reproduction**: [Simplest way to trigger]
**Variables Eliminated**: [What we ruled out]
**Dependencies Checked**: [External factors tested]
**Boundaries Identified**: [Where it works/fails]
**Debug Information**: [Additional logging added]
```

### **Step 4: Hypothesize Root Causes** (10 minutes)
**Objective**: Identify likely root causes (not symptoms)

#### Actions:
- [ ] **List potential root causes** - Focus on underlying issues
- [ ] **Rank by likelihood** - Most probable first
- [ ] **Consider recent changes** - What changed that could cause this
- [ ] **Rule out speculation** - Only evidence-based hypotheses
- [ ] **Identify testing approach** - How to test each hypothesis

#### Documentation:
```markdown
### Root Cause Hypotheses
1. **[Hypothesis 1]**: [Description]
   - **Likelihood**: High/Medium/Low
   - **Evidence**: [Supporting evidence]
   - **Test**: [How to verify]

2. **[Hypothesis 2]**: [Description]
   - **Likelihood**: High/Medium/Low
   - **Evidence**: [Supporting evidence]
   - **Test**: [How to verify]
```

### **Step 5: Test Hypotheses** (Variable time)
**Objective**: Systematically test each hypothesis with minimal changes

#### Actions:
- [ ] **Test highest probability first** - Start with most likely cause
- [ ] **Make minimal, reversible changes** - Easy to undo
- [ ] **Test one thing at a time** - Don't change multiple variables
- [ ] **Document each test** - What was tried, what happened
- [ ] **Revert if unsuccessful** - Clean slate for next test

#### Documentation:
```markdown
### Hypothesis Testing
#### Test 1: [Hypothesis being tested]
**Change Made**: [Minimal change implemented]
**Expected Result**: [What should happen if hypothesis is correct]
**Actual Result**: [What actually happened]
**Conclusion**: [Confirmed/Rejected/Partial]
**Action**: [Kept change/Reverted/Modified]
```

### **Step 6: Implement Solution** (Variable time)
**Objective**: Apply the verified fix cleanly and completely

#### Actions:
- [ ] **Implement verified solution** - Based on successful hypothesis test
- [ ] **Make clean, complete fix** - Not just a workaround
- [ ] **Test thoroughly** - Verify fix works in all scenarios
- [ ] **Check for side effects** - Ensure nothing else broke
- [ ] **Document the fix** - What was changed and why

#### Documentation:
```markdown
### Solution Implementation
**Root Cause Confirmed**: [What was actually wrong]
**Solution Applied**: [What was changed]
**Files Modified**: 
- `path/to/file.py` - [Description of changes]
- `config/setting.yaml` - [Configuration updates]

**Testing Performed**: [How solution was verified]
**Side Effects Checked**: [What else was tested]
```

### **Step 7: Verify Resolution** (10-15 minutes)
**Objective**: Confirm the problem is completely resolved

#### Actions:
- [ ] **Test original problem scenario** - Verify it's fixed
- [ ] **Test edge cases** - Ensure robust solution
- [ ] **Run related tests** - Make sure nothing else broke
- [ ] **Monitor for recurrence** - Check logs, metrics
- [ ] **Get user confirmation** - If applicable

#### Documentation:
```markdown
### Resolution Verification
**Original Problem**: [Status - Fixed/Partially Fixed/Not Fixed]
**Test Results**: 
- **Primary scenario**: [Result]
- **Edge cases**: [Results]
- **Regression tests**: [Results]

**Monitoring**: [What to watch for recurrence]
**User Feedback**: [If applicable]
```

### **Step 8: Document and Learn** (10 minutes)
**Objective**: Capture lessons and prevent recurrence

#### Actions:
- [ ] **Remove temporary debug code** - Clean up any debugging additions
- [ ] **Update documentation** - If process/architecture docs need updates
- [ ] **Create prevention measures** - How to avoid this in future
- [ ] **Update troubleshooting guides** - Add to knowledge base
- [ ] **Complete lessons learned** - Follow session protocol

#### Documentation:
```markdown
### Resolution Summary
**Problem**: [Brief description of what was wrong]
**Root Cause**: [Underlying cause]
**Solution**: [What fixed it]
**Prevention**: [How to avoid in future]
**Time to Resolution**: [Total time spent]

### Lessons for Future
**What Worked**: [Effective troubleshooting approaches]
**What Didn't**: [Approaches that wasted time]
**Improvements**: [How to troubleshoot this type of issue better]
**Prevention**: [How to avoid similar issues]
```

---

## Integration with Session Protocol

### **Session Start**
1. **Execute**: Unified work session setup
2. **Create**: TROUBLESHOOT_LOG.md
3. **Initialize**: Evidence collection folders

### **During Troubleshooting**
1. **Maintain**: SESSION_LOG.md with real-time updates
2. **Collect**: Evidence in organized folders
3. **Document**: Each step in TROUBLESHOOT_LOG.md

### **Session End**
1. **Complete**: Lessons learned section
2. **Create**: CHANGELOG.md with troubleshooting summary
3. **Archive**: All evidence and documentation

---

## Agent-Specific Optimizations

### **For Windsurf**
- Use integrated debugging tools
- Leverage file comparison for before/after analysis
- Use terminal integration for log analysis

### **For Augment**
- Use codebase retrieval to understand related code
- Leverage context engine for similar issue patterns
- Use git commit history for change analysis

### **For Kilo**
- Use collaborative features for team troubleshooting
- Share evidence and findings with team members
- Coordinate resolution efforts

---

## Common Troubleshooting Patterns

### **Performance Issues**
1. **Profile first** - Don't guess where the bottleneck is
2. **Measure baseline** - Know current performance
3. **Test incrementally** - Small changes, measure impact
4. **Consider caching** - But measure first

### **Integration Issues**
1. **Test boundaries** - Where does integration fail
2. **Check versions** - Compatibility issues
3. **Verify configuration** - Settings and environment
4. **Test in isolation** - Remove other variables

### **Logic Errors**
1. **Add strategic logging** - Don't flood logs
2. **Test assumptions** - Verify what you think is true
3. **Check edge cases** - Boundary conditions
4. **Review recent changes** - What changed that could cause this

---

## Success Criteria

### **Troubleshooting Session Complete When**:
- [ ] **Root cause identified** and verified
- [ ] **Solution implemented** and tested
- [ ] **Resolution verified** in all scenarios
- [ ] **Documentation complete** with lessons learned
- [ ] **Prevention measures** identified
- [ ] **Knowledge base updated** for future reference

### **Quality Indicators**:
- [ ] **Systematic approach** followed throughout
- [ ] **Evidence collected** before making changes
- [ ] **Minimal changes** made during testing
- [ ] **Complete documentation** of process and solution
- [ ] **Lessons captured** for future improvement

---

**This enhanced troubleshooting workflow ensures systematic problem-solving with comprehensive documentation and continuous learning.**

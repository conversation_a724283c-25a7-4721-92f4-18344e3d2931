# GUI Component Creation Protocol

**Version**: 1.0  
**Date**: 2025-01-20  
**Status**: ACTIVE

Industry-standard protocol for creating, organizing, and maintaining GUI components in the Flatmate application.

---

## Current State Analysis

### Existing Structure (Inconsistent)
```
gui/_shared_components/
├── base/                    # Panel/Pane architecture only
├── widgets/                 # ALL base widgets (should be in base/)
├── tool_bar_base/          # Toolbar base classes (should be in base/)
├── table_view_v2/          # Complete reusable component (CORRECT)
└── [other components]/
```

### Target Structure (Industry Standard)
```
gui/_shared_components/
├── base/                    # ALL base classes and foundations
│   ├── panels/             # Panel/Pane base classes
│   ├── widgets/            # Base widget classes
│   ├── toolbars/           # Base toolbar classes
│   ├── layouts/            # Layout managers
│   └── mixins/             # Reusable mixins
├── table_view_v2/          # Complete reusable components
├── data_grid/              # Another complete component
├── form_builder/           # Another complete component
└── utils/                  # Shared utilities
```

---

## Component Classification

### 1. **Base Classes** (`base/`)
**Purpose**: Foundation classes that other components inherit from  
**Location**: `gui/_shared_components/base/`  
**Examples**: `BaseWidget`, `BaseToolbar`, `BasePanel`, `LayoutManager`

**Characteristics:**
- Abstract or minimal concrete implementation
- Reusable across multiple components
- Provide common functionality (styling, signals, configuration)
- No business logic - only infrastructure

### 2. **Reusable Components** (`component_name/`)
**Purpose**: Complete, self-contained UI components  
**Location**: `gui/_shared_components/component_name/`  
**Examples**: `table_view_v2`, `data_grid`, `form_builder`

**Characteristics:**
- Complete functionality for specific use case
- Can be dropped into any module
- May have internal sub-components
- Business logic specific to component purpose

### 3. **Shared Utilities** (`utils/`)
**Purpose**: Helper functions and utilities  
**Location**: `gui/_shared_components/utils/`  
**Examples**: Icon helpers, validation utilities, formatting functions

---

## Naming Conventions

### Component Names
- **Folders**: `snake_case` (e.g., `table_view_v2`, `data_grid`)
- **Classes**: `PascalCase` (e.g., `TableViewV2`, `DataGrid`)
- **Files**: `snake_case.py` (e.g., `table_view_v2.py`, `data_grid.py`)

### Base Classes
- **Prefix**: `Base` (e.g., `BaseWidget`, `BaseToolbar`)
- **Managers**: `Manager` suffix (e.g., `LayoutManager`, `ConfigManager`)
- **Mixins**: `Mixin` suffix (e.g., `ConfigurableMixin`, `ThemeMixin`)

### Versioning
- **Major Changes**: `_v2`, `_v3` suffix (e.g., `table_view_v2`)
- **Legacy**: `_legacy` suffix for preserved old versions
- **Experimental**: `_experimental` suffix for testing

---

## File Organization Protocol

### Base Classes Structure
```
base/
├── __init__.py              # Export all base classes
├── widgets/
│   ├── __init__.py
│   ├── base_widget.py       # Foundation widget class
│   ├── base_button.py       # Button base class
│   ├── base_input.py        # Input field base class
│   └── base_container.py    # Container base class
├── toolbars/
│   ├── __init__.py
│   ├── base_toolbar.py      # Toolbar foundation
│   ├── layout_manager.py    # Toolbar layout management
│   └── base_toolbar_button.py
├── panels/
│   ├── __init__.py
│   ├── base_panel.py        # Panel foundation
│   └── base_pane.py         # Pane foundation
└── mixins/
    ├── __init__.py
    ├── configurable_mixin.py # Configuration management
    ├── themeable_mixin.py    # Theme support
    └── persistent_mixin.py   # State persistence
```

### Reusable Component Structure
```
component_name/
├── __init__.py              # Main component export
├── component_name.py        # Main component class
├── config.py               # Component configuration
├── components/             # Internal sub-components
│   ├── __init__.py
│   ├── sub_component_1.py
│   └── sub_component_2.py
├── utils/                  # Component-specific utilities
│   ├── __init__.py
│   └── helper_functions.py
├── styles/                 # Component-specific styles
│   ├── __init__.py
│   └── component_styles.py
└── tests/                  # Component tests
    ├── __init__.py
    └── test_component.py
```

---

## Component Creation Checklist

### Phase 1: Planning
- [ ] **Classify Component**: Base class, reusable component, or utility?
- [ ] **Choose Location**: Based on classification and naming conventions
- [ ] **Define Interface**: What methods, signals, and properties will it expose?
- [ ] **Identify Dependencies**: What base classes or other components does it need?
- [ ] **Plan Configuration**: What should be configurable vs. hardcoded?

### Phase 2: Base Class Design
- [ ] **Inherit Appropriately**: Use existing base classes where possible
- [ ] **Follow Patterns**: Match existing component patterns
- [ ] **Implement Configuration**: Use consistent configuration approach
- [ ] **Add Documentation**: Comprehensive docstrings and examples
- [ ] **Design for Extension**: Make it easy for others to extend

### Phase 3: Implementation
- [ ] **Create Folder Structure**: Follow protocol structure
- [ ] **Implement Main Class**: Core functionality
- [ ] **Add Configuration**: Config file and management
- [ ] **Create Sub-components**: If needed, following same patterns
- [ ] **Add Styling**: Consistent with application theme
- [ ] **Write Tests**: Unit tests for core functionality

### Phase 4: Integration
- [ ] **Update __init__.py**: Export new component
- [ ] **Test Integration**: Verify it works in real application
- [ ] **Update Documentation**: Add to component registry
- [ ] **Create Examples**: Usage examples for other developers
- [ ] **Performance Test**: Ensure acceptable performance

---

## Configuration Protocol

### Standard Configuration Pattern
```python
# config.py
from fm.core.config import BaseConfig

class ComponentConfig(BaseConfig):
    """Configuration for ComponentName."""
    
    def __init__(self):
        super().__init__()
        self.default_size = (800, 600)
        self.theme = "dark"
        self.auto_save = True
        self.max_items = 1000
    
    def validate(self):
        """Validate configuration values."""
        if self.max_items < 1:
            raise ValueError("max_items must be positive")

# component_name.py
class ComponentName(BaseWidget):
    def __init__(self, config: ComponentConfig = None):
        super().__init__()
        self.config = config or ComponentConfig()
        self.config.validate()
        self._init_ui()
```

### Configuration Hierarchy
1. **Default Values**: In config class
2. **User Preferences**: From user settings
3. **Runtime Parameters**: Passed to constructor
4. **Environment**: From environment variables

---

## Styling Protocol

### Theme Integration
```python
# styles/component_styles.py
def get_component_stylesheet(theme="dark"):
    """Get stylesheet for component based on theme."""
    base_styles = get_base_styles(theme)
    
    component_styles = f"""
    ComponentName {{
        background-color: {base_styles['background']};
        color: {base_styles['text']};
        border: 1px solid {base_styles['border']};
    }}
    
    ComponentName:hover {{
        border-color: {base_styles['accent']};
    }}
    """
    
    return component_styles
```

### Style Application
```python
# In component __init__
def _apply_styling(self):
    """Apply component styling."""
    stylesheet = get_component_stylesheet(self.config.theme)
    self.setStyleSheet(stylesheet)
```

---

## Testing Protocol

### Required Tests
1. **Unit Tests**: Core functionality
2. **Integration Tests**: With other components
3. **UI Tests**: Visual and interaction testing
4. **Performance Tests**: Memory and speed
5. **Configuration Tests**: All config options

### Test Structure
```python
# tests/test_component.py
import pytest
from PySide6.QtWidgets import QApplication
from ..component_name import ComponentName, ComponentConfig

class TestComponentName:
    def test_initialization(self):
        """Test component initializes correctly."""
        component = ComponentName()
        assert component is not None
        assert isinstance(component.config, ComponentConfig)
    
    def test_configuration(self):
        """Test configuration handling."""
        config = ComponentConfig()
        config.max_items = 500
        component = ComponentName(config)
        assert component.config.max_items == 500
    
    def test_signals(self):
        """Test signal emission."""
        component = ComponentName()
        signal_received = False
        
        def on_signal():
            nonlocal signal_received
            signal_received = True
        
        component.some_signal.connect(on_signal)
        component.trigger_signal()
        assert signal_received
```

---

## Migration Strategy

### Phase 1: Establish Base Structure
1. Create `base/` folder structure
2. Move existing base classes to appropriate locations
3. Update imports across codebase
4. Test all existing functionality

### Phase 2: Standardize Existing Components
1. Audit existing components against protocol
2. Refactor non-compliant components
3. Add missing configuration and documentation
4. Update tests

### Phase 3: Future Development
1. All new components follow protocol
2. Regular audits for compliance
3. Update protocol based on lessons learned
4. Maintain documentation

---

## Quality Gates

### Before Component Creation:
- [ ] Protocol reviewed and understood
- [ ] Component classification determined
- [ ] Naming conventions verified
- [ ] Dependencies identified

### Before Component Completion:
- [ ] All checklist items completed
- [ ] Tests written and passing
- [ ] Documentation complete
- [ ] Integration verified
- [ ] Performance acceptable

### Before Component Release:
- [ ] Code review completed
- [ ] User acceptance testing done
- [ ] Documentation updated
- [ ] Examples created
- [ ] Migration path documented (if needed)

---

## Success Metrics

### Code Quality:
- Consistent naming and structure
- High test coverage (>80%)
- Clear documentation
- Minimal technical debt

### Developer Experience:
- Easy to find and use components
- Clear examples and documentation
- Consistent patterns across components
- Minimal learning curve

### Maintainability:
- Easy to extend and modify
- Clear separation of concerns
- Minimal coupling between components
- Well-defined interfaces

---

---

## Practical Examples

### Example 1: Creating a Base Widget

```python
# gui/_shared_components/base/widgets/base_input.py
from PySide6.QtWidgets import QLineEdit
from PySide6.QtCore import Signal
from ..mixins.configurable_mixin import ConfigurableMixin
from ..mixins.themeable_mixin import ThemeableMixin

class BaseInput(QLineEdit, ConfigurableMixin, ThemeableMixin):
    """Base class for all input widgets in the application."""

    # Standard signals
    value_changed = Signal(str)
    validation_failed = Signal(str)  # error message

    def __init__(self, placeholder="", parent=None):
        super().__init__(parent)
        ConfigurableMixin.__init__(self)
        ThemeableMixin.__init__(self)

        self.setPlaceholderText(placeholder)
        self._init_base_input()
        self._connect_signals()

    def _init_base_input(self):
        """Initialize base input functionality."""
        self.setObjectName("BaseInput")
        self._apply_base_styling()

    def _connect_signals(self):
        """Connect internal signals."""
        self.textChanged.connect(self._on_text_changed)

    def _on_text_changed(self, text):
        """Handle text changes with validation."""
        if self.validate_input(text):
            self.value_changed.emit(text)
        else:
            self.validation_failed.emit("Invalid input")

    def validate_input(self, text):
        """Override in subclasses for specific validation."""
        return True

    def _apply_base_styling(self):
        """Apply base styling - can be overridden."""
        self.apply_theme_styles("input")
```

### Example 2: Creating a Reusable Component

```python
# gui/_shared_components/search_builder/search_builder.py
from PySide6.QtWidgets import QWidget, QVBoxLayout
from PySide6.QtCore import Signal
from ..base.widgets.base_widget import BaseWidget
from .config import SearchBuilderConfig
from .components.query_builder import QueryBuilder
from .components.result_preview import ResultPreview

class SearchBuilder(BaseWidget):
    """Advanced search builder component for non-technical users."""

    # Component signals
    search_query_built = Signal(dict)  # query object
    search_executed = Signal(str)      # query string

    def __init__(self, config: SearchBuilderConfig = None, parent=None):
        super().__init__(parent)
        self.config = config or SearchBuilderConfig()
        self.config.validate()

        self._init_ui()
        self._connect_signals()
        self._apply_styling()

    def _init_ui(self):
        """Initialize the search builder UI."""
        layout = QVBoxLayout(self)

        # Create sub-components
        self.query_builder = QueryBuilder(self.config.query_config)
        self.result_preview = ResultPreview(self.config.preview_config)

        # Add to layout
        layout.addWidget(self.query_builder)
        layout.addWidget(self.result_preview)

    def _connect_signals(self):
        """Connect component signals."""
        self.query_builder.query_changed.connect(self._on_query_changed)
        self.result_preview.execute_requested.connect(self._on_execute_requested)

    def _on_query_changed(self, query_dict):
        """Handle query changes."""
        self.result_preview.update_preview(query_dict)
        self.search_query_built.emit(query_dict)

    def _on_execute_requested(self, query_string):
        """Handle search execution."""
        self.search_executed.emit(query_string)
```

### Example 3: Configuration Class

```python
# gui/_shared_components/search_builder/config.py
from fm.core.config import BaseConfig

class SearchBuilderConfig(BaseConfig):
    """Configuration for SearchBuilder component."""

    def __init__(self):
        super().__init__()

        # UI Configuration
        self.show_preview = True
        self.max_preview_results = 10
        self.enable_advanced_mode = False

        # Query Configuration
        self.max_conditions = 20
        self.supported_operators = ['equals', 'contains', 'starts_with', 'greater_than']
        self.default_operator = 'contains'

        # Sub-component configs
        self.query_config = QueryBuilderConfig()
        self.preview_config = ResultPreviewConfig()

    def validate(self):
        """Validate configuration values."""
        if self.max_preview_results < 1:
            raise ValueError("max_preview_results must be positive")

        if self.default_operator not in self.supported_operators:
            raise ValueError(f"default_operator must be one of {self.supported_operators}")

        # Validate sub-configs
        self.query_config.validate()
        self.preview_config.validate()
```

---

## Implementation Roadmap

### Immediate Actions (Next Session)
1. **Create base folder structure** following protocol
2. **Move toolbar base classes** to `base/toolbars/`
3. **Update imports** in toolbar components
4. **Test existing functionality** still works

### Short Term (Next 2-3 Sessions)
1. **Move widget base classes** to `base/widgets/`
2. **Create configuration mixins** for reusable config patterns
3. **Standardize existing components** to follow protocol
4. **Create component registry** documentation

### Medium Term (Next Month)
1. **Audit all existing components** against protocol
2. **Refactor non-compliant components** gradually
3. **Create comprehensive examples** for each component type
4. **Establish testing standards** and coverage requirements

### Long Term (Ongoing)
1. **All new components** follow protocol automatically
2. **Regular protocol reviews** and improvements
3. **Developer training** on component creation
4. **Automated compliance checking** in CI/CD

---

**This protocol ensures all GUI components follow industry-standard patterns and maintain consistency across the application.**

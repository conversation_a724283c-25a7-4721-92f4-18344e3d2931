# Development Protocols - Master Reference

**Purpose**: Centralized location for all development protocols and workflows  
**Status**: Reference copy - originals may be in other locations  
**Maintained**: Links to authoritative versions  

---

## Quick Navigation

### **🚀 Start Here**
- **New to the system?** → `QUICK_REFERENCE/workflow-quick-start.md`
- **Starting a work session?** → `CORE/unified-work-session-protocol.md`
- **Need a template?** → `TEMPLATES/` folder

### **📋 Core Workflows (Centralized & Optimized)**
- **Work Sessions** → `WORKFLOWS/unified-work-session.md`
- **Troubleshooting** → `WORKFLOWS/troubleshoot-enhanced.md`
- **GUI Components** → `WORKFLOWS/gui-component-creation.md`

### **🎯 Agent-Specific Pointers**
- **Windsurf** → `.windsurf/workflows/` (pointers to centralized workflows)
- **Augment** → `.augment/workflows/` (pointers with context engine optimizations)
- **<PERSON><PERSON>** → `.kilo/workflows/` (pointers with collaboration features)

---

## File Organization

```
_PROTOCOLS/
├── README.md                     # This file - master navigation
├── WORKFLOWS/                    # Centralized optimized workflows
│   ├── unified-work-session.md  # Master workflow for all sessions
│   ├── troubleshoot-enhanced.md # Enhanced systematic troubleshooting
│   └── gui-component-creation.md # GUI component development
├── CORE/                         # Essential protocols for all work
│   └── README.md                # Links to authoritative protocol locations
├── SPECIALIZED/                  # Domain-specific protocols
│   └── [Links to specialized protocols]
├── TEMPLATES/                    # Copy-paste templates
│   └── [Links to template locations]
└── QUICK_REFERENCE/             # Fast lookup guides
    └── [Links to quick reference guides]
```

---

## Authoritative Locations

**⚠️ IMPORTANT**: Some protocols live in their authoritative locations for tool integration:

### **AI Assistant Rules** (`.augment/rules/`)
- `unified-work-session-protocol.md` - **AUTHORITATIVE**
- `feature-protocol_v1.1.md` - **AUTHORITATIVE**
- `update-docs.md` - **AUTHORITATIVE**
- `self-improvement-protocol.md` - **AUTHORITATIVE**

### **IDE Workflows** (`.windsurf/workflows/`)
- `trouble-shoot.md` - **AUTHORITATIVE**

### **Architecture Documentation** (`flatmate/DOCS/_ARCHITECTURE/`)
- `GUI_COMPONENT_CREATION_PROTOCOL.md` - **AUTHORITATIVE**
- `SESSION_LOG_TEMPLATE.md` - **AUTHORITATIVE**
- All quick reference guides - **AUTHORITATIVE**

### **This Folder** (`flatmate/DOCS/_PROTOCOLS/`)
- **REFERENCE COPIES** for easy navigation
- **LINKS** to authoritative versions
- **CONSOLIDATED VIEW** of all protocols

---

## Protocol Status

| Protocol | Status | Location | Last Updated |
|----------|--------|----------|--------------|
| **Unified Work Session** | ✅ Active | `.augment/rules/` | 2025-01-20 |
| **Feature Development** | ✅ Active | `.augment/rules/` | 2025-01-20 |
| **Documentation Update** | ✅ Active | `.augment/rules/` | 2025-01-20 |
| **Troubleshooting** | ✅ Active | `.windsurf/workflows/` | 2025-01-20 |
| **GUI Component Creation** | ✅ Active | `DOCS/_ARCHITECTURE/` | 2025-01-20 |
| **Self-Improvement** | ✅ Active | `.augment/rules/` | 2025-01-20 |

---

## Quick Start Checklist

### **For Your Next Work Session**:
- [ ] **Classify work type**: FEATURE | REFACTOR | TROUBLESHOOT | MAINTENANCE
- [ ] **Create session folder**: `flatmate/DOCS/_FEATURES/<session_name>/`
- [ ] **Copy session template**: From `TEMPLATES/session-log-template.md`
- [ ] **Follow appropriate protocol**: Based on work type
- [ ] **Document lessons learned**: Complete at session end
- [ ] **Create changelog**: Following documentation protocol

### **For Weekly Reviews**:
- [ ] **Run lesson review**: `python scripts/weekly_lesson_review.py`
- [ ] **Identify patterns**: What recurring themes emerged?
- [ ] **Update protocols**: Based on lessons learned
- [ ] **Plan improvements**: For next week

### **For Monthly Evolution**:
- [ ] **Review weekly reports**: Look for systemic patterns
- [ ] **Update core protocols**: Based on accumulated lessons
- [ ] **Document changes**: In protocol evolution log
- [ ] **Communicate updates**: To team/future self

---

## Integration Points

### **With Development Tools**
- **VS Code**: Snippets and tasks for protocol execution
- **Git**: Commit templates and hooks for protocol compliance
- **AI Assistants**: Context and validation during work

### **With Documentation System**
- **Session Logs**: Real-time documentation during work
- **Changelogs**: Session summaries and lessons learned
- **Architecture Docs**: Updated based on protocol outcomes

### **With Quality Assurance**
- **Testing**: Protocols include testing requirements
- **Code Review**: Protocol compliance checked in reviews
- **Continuous Improvement**: Lessons feed back into protocol updates

---

## Getting Help

### **Can't Find a Protocol?**
1. **Check this README** for quick navigation
2. **Search the index**: `PROTOCOL_REFERENCE_INDEX.md` in `_ARCHITECTURE/`
3. **Use file search**: `grep -r "protocol_name" flatmate/DOCS/`

### **Protocol Not Working?**
1. **Document the issue** in your session log
2. **Note in lessons learned** section
3. **Flag for weekly review** and protocol update

### **Need New Protocol?**
1. **Document the need** in session logs
2. **Identify patterns** across multiple sessions
3. **Create protocol** following existing patterns
4. **Update this index** and reference documentation

---

## Success Metrics

### **Protocol Adoption**
- **Usage Rate**: % of sessions following protocols
- **Compliance**: % of protocol steps completed
- **Effectiveness**: Time saved through protocol use

### **Quality Improvements**
- **Documentation**: Completeness and clarity
- **Code Quality**: Architecture and maintainability
- **Process Efficiency**: Time to complete tasks

### **Developer Experience**
- **Satisfaction**: How enjoyable the process is
- **Confidence**: How confident decisions are
- **Learning**: How quickly improvements are adopted

---

**This folder provides centralized access to all development protocols. Use it as your starting point for any development work.**
